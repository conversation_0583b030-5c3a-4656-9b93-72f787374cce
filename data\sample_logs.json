[{"timestamp": "2025-08-13T08:00:00Z", "source_ip": "*************", "destination_ip": "********", "port": 22, "event": "Failed SSH login", "severity": "warning", "action": "blocked"}, {"timestamp": "2025-08-13T08:05:00Z", "source_ip": "*************", "destination_ip": "********", "port": 3389, "event": "RDP brute force attempt", "severity": "critical", "action": "dropped"}, {"timestamp": "2025-08-13T08:05:05Z", "source_ip": "*************", "destination_ip": "********", "port": 3389, "event": "RDP brute force attempt", "severity": "critical", "action": "dropped"}, {"timestamp": "2025-08-13T08:10:00Z", "source_ip": "*************", "destination_ip": "********", "port": 80, "event": "Web attack", "severity": "warning", "action": "blocked"}, {"timestamp": "2025-08-13T08:15:00Z", "source_ip": "*************", "destination_ip": "********", "port": 443, "event": "SSL attack", "severity": "critical", "action": "dropped"}, {"timestamp": "2025-08-13T08:20:00Z", "source_ip": "*************", "destination_ip": "********", "port": 53, "event": "DNS attack", "severity": "info", "action": "allowed"}, {"timestamp": "2025-08-13T08:25:00Z", "source_ip": "*************", "destination_ip": "********", "port": 8080, "event": "Proxy attack", "severity": "warning", "action": "blocked"}, {"timestamp": "2025-08-13T08:30:00Z", "source_ip": "*************", "destination_ip": "********", "port": 8081, "event": "Proxy attack", "severity": "critical", "action": "dropped"}, {"timestamp": "2025-08-13T08:35:00Z", "source_ip": "*************", "destination_ip": "********", "port": 8082, "event": "Proxy attack", "severity": "info", "action": "allowed"}, {"timestamp": "2025-08-13T08:40:00Z", "source_ip": "*************", "destination_ip": "********", "port": 8083, "event": "Proxy attack", "severity": "warning", "action": "blocked"}]